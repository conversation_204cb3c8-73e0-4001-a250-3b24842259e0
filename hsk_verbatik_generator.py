import json
import os
import asyncio
import aiohttp
import aiofiles
import requests
from typing import Optional
import time

# Verbatik API configuration
API_KEY = os.getenv("VERBATIK_API_KEY", "vbt_pDMUIEac9JnycAj2Ab5kHDC4XMTsuVkd")
BASE_URL = "https://api.verbatik.com/api/v1/tts"

# Xiaoxiao voice configuration - optimized for Chinese HSK learning
VOICE_CONFIG = {
    "voice_id": "Xiaoxiao",  # Best female Chinese voice for educational content
    "speed": "0.9",         # Slightly slower for clear pronunciation
    "pitch": "0",           # Natural pitch
    "volume": "0",          # Normal volume
    "emphasis": "moderate", # Clear emphasis for tones
}

async def generate_audio_verbatik(session: aiohttp.ClientSession, text: str, output_path: str) -> bool:
    """Generate audio using Verbatik API with optimized settings for HSK learning"""

    headers = {
        'Content-Type': 'application/ssml+xml',
        'Authorization': f'Bearer {API_KEY}',
        'X-Voice-ID': VOICE_CONFIG["voice_id"]
    }

    # Start with simple text, then we can add SSML if needed
    data = text
    
    try:
        async with session.post(BASE_URL, headers=headers, data=data) as response:
            if response.status == 200:
                # Save the audio file
                async with aiofiles.open(output_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                return True
            else:
                error_text = await response.text()
                print(f"API Error {response.status}: {error_text}")
                return False
                
    except Exception as e:
        print(f"Error generating audio: {e}")
        return False

async def process_hsk_level(session: aiohttp.ClientSession, level: int):
    """Process all characters for a specific HSK level"""
    print(f"Processing HSK level {level}...")
    
    # Create directory for this level
    os.makedirs(f'HSK{level}', exist_ok=True)
    
    # Load JSON data
    json_path = f'hsk-json-all/hsk-level-{level}.json'
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
    except FileNotFoundError:
        print(f"❌ JSON file not found: {json_path}")
        return
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON file: {json_path}")
        return
    
    successful = 0
    failed = 0
    total_size = 0
    
    # Generate audio for each character
    for i, char_data in enumerate(characters):
        char_id = char_data.get('id', f"unknown_{i}")
        hanzi = char_data.get('hanzi', '')
        pinyin = char_data.get('pinyin', '')
        
        if not hanzi:
            print(f"⚠️ Skipping entry {char_id}: No hanzi found")
            continue
        
        # Output path
        output_path = f'HSK{level}/{char_id}.mp3'
        
        # Skip if file already exists
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"⏭️ Skipping {hanzi} (ID: {char_id}), file already exists ({file_size} bytes)")
            total_size += file_size
            successful += 1
            continue
        
        print(f"🎵 Generating audio for {hanzi} (ID: {char_id}, Pinyin: {pinyin})")
        
        # Generate audio
        success = await generate_audio_verbatik(session, hanzi, output_path)
        
        if success:
            # Check file size
            file_size = os.path.getsize(output_path)
            total_size += file_size
            size_kb = file_size / 1024
            
            if size_kb <= 4:
                print(f"✅ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Perfect size!")
            elif size_kb <= 6:
                print(f"✅ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Good size")
            else:
                print(f"⚠️ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Large file")
            
            successful += 1
        else:
            print(f"❌ Failed to generate audio for {hanzi}")
            failed += 1
        
        # Rate limiting: small delay between requests to be respectful
        await asyncio.sleep(0.2)
    
    avg_size = (total_size / successful) if successful > 0 else 0
    avg_size_kb = avg_size / 1024
    
    print(f"HSK Level {level} completed:")
    print(f"  ✅ Successful: {successful}")
    print(f"  ❌ Failed: {failed}")
    print(f"  📊 Average file size: {avg_size:.0f} bytes ({avg_size_kb:.1f}KB)")
    print(f"  💾 Total size: {total_size/1024:.1f}KB")

async def test_api_connection(session: aiohttp.ClientSession) -> bool:
    """Test the Verbatik API connection with a simple request"""
    print("🔍 Testing Verbatik API connection...")
    
    test_output = "test_audio.mp3"
    success = await generate_audio_verbatik(session, "测试", test_output)
    
    if success and os.path.exists(test_output):
        file_size = os.path.getsize(test_output)
        print(f"✅ API test successful! Generated test file: {file_size} bytes")
        os.remove(test_output)  # Clean up test file
        return True
    else:
        print("❌ API test failed!")
        return False

async def main():
    """Main function to process all HSK levels"""
    print("🚀 Starting HSK audio generation with Verbatik (Xiaoxiao voice)...")
    print("🎯 Optimized for Chinese tone clarity and 2-4KB file sizes")
    
    # Validate API key
    if not API_KEY or len(API_KEY) < 20:
        print("❌ Invalid or missing API key!")
        print("💡 Set your Verbatik API key as environment variable: export VERBATIK_API_KEY=your_key_here")
        return
    
    # Create aiohttp session with optimized settings
    connector = aiohttp.TCPConnector(limit=5)  # Conservative connection limit
    timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        
        # Test API connection first
        if not await test_api_connection(session):
            print("❌ Cannot proceed without working API connection")
            return
        
        print(f"🎤 Using Xiaoxiao voice with educational optimization")
        print(f"⚙️ Settings: Speed={VOICE_CONFIG['speed']}, Emphasis={VOICE_CONFIG['emphasis']}")
        
        # Process each HSK level (1-6)
        start_time = time.time()
        
        for level in range(1, 7):
            level_start = time.time()
            await process_hsk_level(session, level)
            level_time = time.time() - level_start
            print(f"⏱️ HSK{level} completed in {level_time:.1f} seconds\n")
        
        total_time = time.time() - start_time
        print("🎉 All HSK levels processed!")
        print(f"⏱️ Total time: {total_time:.1f} seconds")
        print("💡 Files optimized for HSK learning with clear Chinese tones!")

# Run the async main function
if __name__ == "__main__":
    asyncio.run(main())
