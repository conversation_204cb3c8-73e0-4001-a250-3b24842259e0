#!/usr/bin/env python3
"""
Quick test script to verify Murf.ai file size optimization
"""

import asyncio
import aiohttp
import os
from hsk_murf_generator import generate_audio_murf, check_file_size

# Test configuration
API_KEY = "ap2_882b723b-83d3-4690-bbfe-76bc2bd0aa03"
VOICE_CONFIG = {"voice_id": "zh-CN-wei", "style": "Calm"}

async def test_file_sizes():
    """Test file size optimization with sample Chinese characters"""
    test_characters = ["你", "好", "学", "习", "中文"]
    
    print("🧪 Testing Murf.ai educational audio generation...")
    print(f"🎯 Target: 2-6KB per file (optimized for tone clarity)")
    print(f"🎓 Educational mode: Clear Chinese pronunciation for HSK learning")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        for i, char in enumerate(test_characters):
            output_path = f"test_size_{i+1}_{char}.mp3"
            print(f"\n🎵 Testing character: {char}")
            
            success = await generate_audio_murf(session, char, VOICE_CONFIG, output_path)
            
            if success:
                file_info = check_file_size(output_path)
                if file_info["exists"]:
                    status = "✅ OPTIMAL" if file_info["is_optimal"] else "⚠️ TOO LARGE"
                    print(f"   {status}: {file_info['size_kb']}KB ({file_info['size_bytes']} bytes)")
                else:
                    print(f"   ❌ File not found")
            else:
                print(f"   ❌ Generation failed")
    
    print("\n" + "=" * 50)
    print("🔍 Test files generated. Check the sizes:")
    
    total_size = 0
    file_count = 0
    
    for i in range(len(test_characters)):
        filename = f"test_size_{i+1}_{test_characters[i]}.mp3"
        if os.path.exists(filename):
            file_info = check_file_size(filename)
            total_size += file_info["size_kb"]
            file_count += 1
            print(f"   📁 {filename}: {file_info['size_kb']}KB")
    
    if file_count > 0:
        avg_size = round(total_size / file_count, 2)
        print(f"\n📊 Average file size: {avg_size}KB")
        print(f"📊 Total size: {round(total_size, 2)}KB for {file_count} files")
        
        if avg_size <= 4:
            print("🎉 SUCCESS: Average file size is within target!")
        else:
            print("⚠️ WARNING: Average file size exceeds target")

if __name__ == "__main__":
    asyncio.run(test_file_sizes())
