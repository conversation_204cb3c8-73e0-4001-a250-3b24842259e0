#!/usr/bin/env python3
"""
HSK EdgeTTS Failed Audio Regenerator
Regenerates the 89 failed MP3 files using Microsoft EdgeTTS with Xiao<PERSON><PERSON><PERSON> voice.
Specifically targets the files that failed during the MP3 to Opus conversion.
"""

import json
import os
import asyncio
import edge_tts
import time

# Define the voice to use - <PERSON><PERSON><PERSON><PERSON> is a high-quality female Mandarin voice
VOICE = "zh-CN-XiaoXiaoNeural"

# Failed files by HSK level (from conversion report)
FAILED_FILES = {
    2: [198, 241, 268, 296],
    3: [311, 358, 484, 520, 532, 561, 592],
    4: [664, 751, 787, 829, 930, 940, 1014, 1036, 1056, 1068],
    5: [1201, 1203, 1205, 1207, 1209, 1211, 1213, 1215, 1217, 1219, 1221, 1223, 1225, 1227, 1229, 1231, 1233, 1235, 1237, 1239, 1241, 1243, 1245, 1247, 1249, 1251, 1253, 1255, 1257, 1259, 2095],
    6: [2574, 2733, 2776, 2897, 2992, 2996, 3019, 3042, 3056, 3088, 3105, 3226, 3372, 3453, 3458, 3476, 3534, 3555, 3625, 3642, 3655, 3689, 3710, 3973, 3987, 4082, 4111, 4124, 4186, 4189, 4489, 4590, 4592, 4594, 4598, 4718, 4806, 4821, 4908]
}

async def generate_audio(text, output_path):
    """Generate audio file using edge-tts with the specified voice"""
    try:
        communicate = edge_tts.Communicate(text, VOICE)
        await communicate.save(output_path)
        # Small delay to avoid rate limiting
        await asyncio.sleep(0.1)
        return True
    except Exception as e:
        print(f"❌ EdgeTTS error: {e}")
        return False

def load_character_data(hsk_level, char_id):
    """Load character data from HSK JSON files"""
    json_path = f'hsk-json-all/hsk-level-{hsk_level}.json'
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
        
        # Find character by ID
        for char_data in characters:
            if char_data.get('id') == char_id:
                hanzi = char_data.get('hanzi', '')
                pinyin = char_data.get('pinyin', '')
                return hanzi, pinyin
        
        print(f"⚠️ Character ID {char_id} not found in HSK{hsk_level}")
        return "", ""
        
    except Exception as e:
        print(f"❌ Error loading HSK{hsk_level} data: {e}")
        return "", ""

async def process_failed_files_for_level(level, failed_ids):
    """Process failed files for a specific HSK level"""
    print(f"\n🎵 Processing HSK{level}: {len(failed_ids)} failed files")
    
    # Create directory for this level
    os.makedirs(f'hsk{level}', exist_ok=True)
    
    successful = 0
    failed = 0
    total_size = 0
    
    for i, char_id in enumerate(failed_ids, 1):
        print(f"📥 [{i}/{len(failed_ids)}] Processing ID {char_id}...")
        
        # Load character data
        hanzi, pinyin = load_character_data(level, char_id)
        if not hanzi:
            print(f"⚠️ Skipping ID {char_id}: No character data found")
            failed += 1
            continue
        
        # Output path
        output_path = f'hsk{level}/{char_id}.mp3'
        
        # Check if file already exists and is valid
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            if file_size > 1000:  # If file is larger than 1KB, assume it's valid
                print(f"⏭️ Skipping '{hanzi}' (ID: {char_id}), valid file already exists ({file_size} bytes)")
                successful += 1
                total_size += file_size
                continue
            else:
                print(f"🔄 Replacing corrupted file for '{hanzi}' (ID: {char_id})")
        
        print(f"🎵 Generating audio for '{hanzi}' (拼音: {pinyin})")
        
        try:
            # Generate audio using EdgeTTS
            success = await generate_audio(hanzi, output_path)
            
            if success and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                total_size += file_size
                size_kb = file_size / 1024
                
                print(f"✅ Created audio for '{hanzi}' ({file_size} bytes / {size_kb:.1f}KB)")
                successful += 1
            else:
                print(f"❌ Failed to generate audio for '{hanzi}'")
                failed += 1
                
        except Exception as e:
            print(f"❌ Error generating audio for '{hanzi}': {e}")
            failed += 1
    
    # Summary for this level
    avg_size = (total_size / successful) if successful > 0 else 0
    avg_size_kb = avg_size / 1024
    
    print(f"\n📊 HSK{level} Summary:")
    print(f"  ✅ Successful: {successful}")
    print(f"  ❌ Failed: {failed}")
    print(f"  📈 Average file size: {avg_size:.0f} bytes ({avg_size_kb:.1f}KB)")
    print(f"  💾 Total size: {total_size/1024:.1f}KB")
    
    return successful, failed, total_size

async def test_edgetts_connection():
    """Test EdgeTTS connection with a simple request"""
    print("🔍 Testing EdgeTTS connection...")
    
    test_output = "test_edgetts.mp3"
    success = await generate_audio("测试", test_output)
    
    if success and os.path.exists(test_output):
        file_size = os.path.getsize(test_output)
        print(f"✅ EdgeTTS test successful! ({file_size} bytes)")
        os.remove(test_output)  # Clean up test file
        return True
    else:
        print("❌ EdgeTTS test failed!")
        return False

async def main():
    """Main function to regenerate all failed audio files"""
    print("🚀 HSK EdgeTTS Failed Audio Regenerator")
    print("🎯 Target: 89 failed files across HSK levels 2-6")
    print(f"🎤 Using Microsoft EdgeTTS with {VOICE}")
    
    # Test EdgeTTS connection first
    if not await test_edgetts_connection():
        print("❌ Cannot proceed without working EdgeTTS connection")
        return
    
    # Calculate total files
    total_files = sum(len(files) for files in FAILED_FILES.values())
    print(f"📊 Total files to regenerate: {total_files}")
    
    start_time = time.time()
    total_successful = 0
    total_failed = 0
    total_size = 0
    
    # Process each HSK level with failed files
    for level in sorted(FAILED_FILES.keys()):
        failed_ids = FAILED_FILES[level]
        if not failed_ids:
            continue
            
        level_start = time.time()
        successful, failed, size = await process_failed_files_for_level(level, failed_ids)
        level_time = time.time() - level_start
        
        total_successful += successful
        total_failed += failed
        total_size += size
        
        print(f"⏱️ HSK{level} completed in {level_time:.1f} seconds")
    
    # Final summary
    total_time = time.time() - start_time
    avg_size = (total_size / total_successful) if total_successful > 0 else 0
    
    print(f"\n🎉 Regeneration Complete!")
    print(f"⏱️ Total time: {total_time:.1f} seconds")
    print(f"✅ Total successful: {total_successful}")
    print(f"❌ Total failed: {total_failed}")
    print(f"📊 Average file size: {avg_size/1024:.1f}KB")
    print(f"💾 Total size: {total_size/1024:.1f}KB")
    
    if total_successful == total_files:
        print("🏆 Perfect! All failed files have been successfully regenerated!")
        print("💡 Next step: Run convert_mp3_to_opus.py to convert these new MP3 files to Opus")
    else:
        remaining = total_files - total_successful
        print(f"⚠️ {remaining} files still need attention")
    
    print(f"\n📁 Generated files are in: hsk2/, hsk3/, hsk4/, hsk5/, hsk6/")

# Run the async main function
if __name__ == "__main__":
    asyncio.run(main())
