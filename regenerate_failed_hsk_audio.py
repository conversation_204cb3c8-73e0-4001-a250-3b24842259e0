#!/usr/bin/env python3
"""
HSK Failed Audio Regeneration Script
Re-downloads and converts the 89 failed MP3 files to complete the HSK audio library.
Uses Verbatik API with Xiao<PERSON>o voice for consistent Chinese pronunciation.
"""

import json
import os
import requests
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Tuple

# Verbatik API configuration
API_KEY = os.getenv("VERBATIK_API_KEY", "vbt_pDMUIEac9JnycAj2Ab5kHDC4XMTsuVkd")
BASE_URL = "https://api.verbatik.com/api/v1/tts"

# Failed files by HSK level (from conversion report)
FAILED_FILES = {
    2: [198, 241, 268, 296],
    3: [311, 358, 484, 520, 532, 561, 592],
    4: [664, 751, 787, 829, 930, 940, 1014, 1036, 1056, 1068],
    5: [1201, 1203, 1205, 1207, 1209, 1211, 1213, 1215, 1217, 1219, 1221, 1223, 1225, 1227, 1229, 1231, 1233, 1235, 1237, 1239, 1241, 1243, 1245, 1247, 1249, 1251, 1253, 1255, 1257, 1259, 2095],
    6: [2574, 2733, 2776, 2897, 2992, 2996, 3019, 3042, 3056, 3088, 3105, 3226, 3372, 3453, 3458, 3476, 3534, 3555, 3625, 3642, 3655, 3689, 3710, 3973, 3987, 4082, 4111, 4124, 4186, 4189, 4489, 4590, 4592, 4594, 4598, 4718, 4806, 4821, 4908]
}

# Opus conversion configuration
OPUS_CONFIG = {
    "bitrate": "16k",
    "sample_rate": "16000", 
    "channels": "1",
    "application": "voip",
    "frame_duration": "60",
    "packet_loss": "1",
}

def generate_audio_verbatik(text: str, output_path: str) -> bool:
    """Generate audio using Verbatik API with Xiaoxiao voice"""
    
    headers = {
        'Content-Type': 'application/ssml+xml',
        'Authorization': f'Bearer {API_KEY}',
        'X-Voice-ID': 'Xiaoxiao'  # Best female Chinese voice for educational content
    }
    
    try:
        response = requests.post(BASE_URL, headers=headers, data=text, timeout=30)
        
        if response.status_code == 200:
            with open(output_path, 'wb') as f:
                f.write(response.content)
            return True
        else:
            print(f"❌ API Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error generating audio: {e}")
        return False

def convert_mp3_to_opus(mp3_path: str, opus_path: str) -> Tuple[bool, int, int]:
    """Convert MP3 to Opus format and return success status with file sizes"""
    
    try:
        cmd = [
            'ffmpeg',
            '-i', mp3_path,
            '-c:a', 'libopus',
            '-b:a', OPUS_CONFIG["bitrate"],
            '-ar', OPUS_CONFIG["sample_rate"],
            '-ac', OPUS_CONFIG["channels"],
            '-application', OPUS_CONFIG["application"],
            '-frame_duration', OPUS_CONFIG["frame_duration"],
            '-packet_loss', OPUS_CONFIG["packet_loss"],
            '-y',  # Overwrite output file
            opus_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            original_size = os.path.getsize(mp3_path)
            new_size = os.path.getsize(opus_path)
            return True, original_size, new_size
        else:
            print(f"❌ FFmpeg error: {result.stderr}")
            return False, 0, 0
            
    except Exception as e:
        print(f"❌ Error converting to Opus: {e}")
        return False, 0, 0

def load_character_data(hsk_level: int, char_id: int) -> Tuple[str, str]:
    """Load character data from HSK JSON files"""
    
    json_path = f'hsk-json-all/hsk-level-{hsk_level}.json'
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
        
        # Find character by ID
        for char_data in characters:
            if char_data.get('id') == char_id:
                hanzi = char_data.get('hanzi', '')
                pinyin = char_data.get('pinyin', '')
                return hanzi, pinyin
        
        print(f"⚠️ Character ID {char_id} not found in HSK{hsk_level}")
        return "", ""
        
    except Exception as e:
        print(f"❌ Error loading HSK{hsk_level} data: {e}")
        return "", ""

def detect_additional_failed_files() -> Dict[int, List[int]]:
    """Detect any additional failed files by checking missing Opus files"""
    
    additional_failed = {}
    
    for level in range(1, 7):
        opus_folder = f"hsk{level}_opus"
        if not os.path.exists(opus_folder):
            continue
            
        # Get existing opus files
        existing_opus = set()
        for file in os.listdir(opus_folder):
            if file.endswith('.opus'):
                try:
                    file_id = int(file.replace('.opus', ''))
                    existing_opus.add(file_id)
                except ValueError:
                    continue
        
        # Check against expected range based on JSON files
        json_path = f'hsk-json-all/hsk-level-{level}.json'
        if os.path.exists(json_path):
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    characters = json.load(f)
                
                expected_ids = {char.get('id') for char in characters if char.get('id')}
                missing_ids = expected_ids - existing_opus
                
                if missing_ids:
                    # Filter out already known failed files
                    known_failed = set(FAILED_FILES.get(level, []))
                    additional_missing = missing_ids - known_failed
                    
                    if additional_missing:
                        additional_failed[level] = sorted(list(additional_missing))
                        print(f"🔍 Detected additional missing files in HSK{level}: {sorted(list(additional_missing))}")
                        
            except Exception as e:
                print(f"⚠️ Error checking HSK{level}: {e}")
    
    return additional_failed

def regenerate_failed_files():
    """Main function to regenerate all failed audio files"""
    
    print("🚀 Starting HSK Failed Audio Regeneration")
    print("🎯 Target: 89 failed files across HSK levels 2-6 (39 in HSK6, 50 in HSK2-5)")
    print(f"🎤 Using Xiaoxiao voice with Verbatik API")
    
    # Validate API key
    if not API_KEY or len(API_KEY) < 20:
        print("❌ Invalid or missing API key!")
        print("💡 Set your Verbatik API key as environment variable: export VERBATIK_API_KEY=your_key_here")
        return
    
    # Detect any additional failed files
    print("\n🔍 Detecting additional failed files...")
    additional_failed = detect_additional_failed_files()
    
    # Merge with known failed files
    all_failed = FAILED_FILES.copy()
    for level, files in additional_failed.items():
        if level in all_failed:
            all_failed[level].extend(files)
            all_failed[level] = sorted(list(set(all_failed[level])))  # Remove duplicates
        else:
            all_failed[level] = files
    
    # Statistics
    total_files = sum(len(files) for files in all_failed.values())
    print(f"📊 Total files to regenerate: {total_files}")
    
    successful_downloads = 0
    failed_downloads = 0
    successful_conversions = 0
    failed_conversions = 0
    total_original_size = 0
    total_opus_size = 0
    
    start_time = time.time()
    
    # Process each HSK level
    for level in sorted(all_failed.keys()):
        failed_ids = all_failed[level]
        if not failed_ids:
            continue
            
        print(f"\n🎵 Processing HSK{level}: {len(failed_ids)} files")
        
        # Ensure directories exist
        os.makedirs(f'hsk{level}', exist_ok=True)
        os.makedirs(f'hsk{level}_opus', exist_ok=True)
        
        for i, char_id in enumerate(failed_ids, 1):
            print(f"📥 [{i}/{len(failed_ids)}] Processing ID {char_id}...")
            
            # Load character data
            hanzi, pinyin = load_character_data(level, char_id)
            if not hanzi:
                print(f"⚠️ Skipping ID {char_id}: No character data found")
                failed_downloads += 1
                continue
            
            # File paths
            mp3_path = f'hsk{level}/{char_id}.mp3'
            opus_path = f'hsk{level}_opus/{char_id}.opus'
            
            print(f"🎵 Generating audio for '{hanzi}' (拼音: {pinyin})")
            
            # Step 1: Download MP3 from Verbatik
            download_success = generate_audio_verbatik(hanzi, mp3_path)
            
            if download_success:
                mp3_size = os.path.getsize(mp3_path)
                print(f"✅ Downloaded MP3: {mp3_size} bytes ({mp3_size/1024:.1f}KB)")
                successful_downloads += 1
                total_original_size += mp3_size
                
                # Step 2: Convert to Opus
                conversion_success, original_size, opus_size = convert_mp3_to_opus(mp3_path, opus_path)
                
                if conversion_success:
                    total_opus_size += opus_size
                    compression = ((original_size - opus_size) / original_size * 100) if original_size > 0 else 0
                    print(f"✅ Converted to Opus: {opus_size} bytes ({opus_size/1024:.1f}KB) - {compression:.1f}% smaller")
                    successful_conversions += 1
                else:
                    print(f"❌ Failed to convert {char_id} to Opus")
                    failed_conversions += 1
            else:
                print(f"❌ Failed to download {char_id}")
                failed_downloads += 1
            
            # Rate limiting
            time.sleep(0.3)
    
    # Final summary
    total_time = time.time() - start_time
    avg_opus_size = (total_opus_size / successful_conversions) if successful_conversions > 0 else 0
    total_compression = ((total_original_size - total_opus_size) / total_original_size * 100) if total_original_size > 0 else 0
    
    print(f"\n🎉 Regeneration Complete!")
    print(f"⏱️ Total time: {total_time:.1f} seconds")
    print(f"📥 Downloads: ✅ {successful_downloads} | ❌ {failed_downloads}")
    print(f"🔄 Conversions: ✅ {successful_conversions} | ❌ {failed_conversions}")
    print(f"📊 Average Opus size: {avg_opus_size/1024:.1f}KB")
    print(f"🎯 Compression: {total_compression:.1f}% smaller than MP3")
    print(f"💾 Total space saved: {(total_original_size - total_opus_size)/1024:.1f}KB")
    
    if successful_conversions == total_files:
        print("🏆 Perfect! All failed files have been successfully regenerated!")
    else:
        remaining = total_files - successful_conversions
        print(f"⚠️ {remaining} files still need attention")

def test_api_connection() -> bool:
    """Test Verbatik API connection"""
    print("🔍 Testing Verbatik API connection...")
    
    test_output = "test_regeneration.mp3"
    success = generate_audio_verbatik("测试", test_output)
    
    if success and os.path.exists(test_output):
        file_size = os.path.getsize(test_output)
        print(f"✅ API test successful! ({file_size} bytes)")
        os.remove(test_output)
        return True
    else:
        print("❌ API test failed!")
        return False

if __name__ == "__main__":
    # Test API first
    if test_api_connection():
        regenerate_failed_files()
    else:
        print("❌ Cannot proceed without working API connection")
        print("💡 Please check your Verbatik API key and internet connection")
