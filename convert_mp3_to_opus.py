#!/usr/bin/env python3
"""
HSK MP3 to Opus Converter
Converts all existing MP3 files to Opus format for optimal Flutter app performance.
Optimized for 2-4KB file sizes while maintaining clear Chinese pronunciation.
"""

import os
import subprocess
import time
from pathlib import Path
import concurrent.futures
from typing import List, Tuple

# Audio conversion configuration optimized for Chinese speech and small file sizes
OPUS_CONFIG = {
    "bitrate": "16k",          # 16 kbps - perfect for speech, very small files
    "sample_rate": "16000",    # 16kHz - optimal for speech recognition and TTS
    "channels": "1",           # Mono - speech doesn't need stereo
    "application": "voip",     # VoIP mode - optimized for speech
    "frame_duration": "60",    # 60ms frames for better compression
    "packet_loss": "1",        # Optimize for potential packet loss
}

def convert_mp3_to_opus(mp3_path: str, opus_path: str) -> Tuple[bool, str, int, int]:
    """
    Convert a single MP3 file to Opus format
    Returns: (success, filename, original_size, new_size)
    """
    try:
        # FFmpeg command optimized for speech and small file size
        cmd = [
            'ffmpeg',
            '-i', mp3_path,                                    # Input MP3 file
            '-c:a', 'libopus',                                # Opus codec
            '-b:a', OPUS_CONFIG["bitrate"],                   # Bitrate (16k for small files)
            '-ar', OPUS_CONFIG["sample_rate"],                # Sample rate (16kHz for speech)
            '-ac', OPUS_CONFIG["channels"],                   # Mono channel
            '-application', OPUS_CONFIG["application"],       # Optimize for speech (VoIP mode)
            '-frame_duration', OPUS_CONFIG["frame_duration"], # 60ms frames for better compression
            '-packet_loss', OPUS_CONFIG["packet_loss"],       # Optimize for potential packet loss
            '-y',                                             # Overwrite output file
            opus_path
        ]
        
        # Run FFmpeg conversion (suppress output for cleaner logs)
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Get file sizes
            original_size = os.path.getsize(mp3_path)
            new_size = os.path.getsize(opus_path)
            filename = os.path.basename(mp3_path)
            
            return True, filename, original_size, new_size
        else:
            print(f"❌ FFmpeg error for {mp3_path}: {result.stderr}")
            return False, os.path.basename(mp3_path), 0, 0
            
    except Exception as e:
        print(f"❌ Error converting {mp3_path}: {e}")
        return False, os.path.basename(mp3_path), 0, 0

def process_hsk_folder(hsk_folder: str) -> Tuple[int, int, int, int]:
    """
    Process all MP3 files in a single HSK folder
    Returns: (successful, failed, total_original_size, total_new_size)
    """
    print(f"\n🎵 Processing {hsk_folder}...")
    
    # Create output directory
    opus_folder = f"{hsk_folder}_opus"
    os.makedirs(opus_folder, exist_ok=True)
    
    # Find all MP3 files
    mp3_files = list(Path(hsk_folder).glob("*.mp3"))
    
    if not mp3_files:
        print(f"⚠️ No MP3 files found in {hsk_folder}")
        return 0, 0, 0, 0
    
    print(f"📁 Found {len(mp3_files)} MP3 files")
    
    successful = 0
    failed = 0
    total_original_size = 0
    total_new_size = 0
    
    # Process files with progress tracking
    for i, mp3_file in enumerate(mp3_files, 1):
        mp3_path = str(mp3_file)
        filename = mp3_file.stem
        opus_path = os.path.join(opus_folder, f"{filename}.opus")
        
        # Skip if opus file already exists
        if os.path.exists(opus_path):
            original_size = os.path.getsize(mp3_path)
            new_size = os.path.getsize(opus_path)
            total_original_size += original_size
            total_new_size += new_size
            successful += 1
            
            if i % 50 == 0:  # Progress update every 50 files
                print(f"⏭️ Progress: {i}/{len(mp3_files)} ({i/len(mp3_files)*100:.1f}%) - {filename}.opus already exists")
            continue
        
        # Convert file
        success, filename_result, original_size, new_size = convert_mp3_to_opus(mp3_path, opus_path)
        
        if success:
            total_original_size += original_size
            total_new_size += new_size
            successful += 1
            
            # Show progress and file size info
            size_kb = new_size / 1024
            compression_ratio = ((original_size - new_size) / original_size) * 100
            
            if i % 10 == 0:  # Progress update every 10 files
                print(f"✅ Progress: {i}/{len(mp3_files)} ({i/len(mp3_files)*100:.1f}%) - {filename_result} → {size_kb:.1f}KB ({compression_ratio:.1f}% smaller)")
        else:
            failed += 1
            print(f"❌ Failed: {filename_result}")
    
    # Summary for this folder
    avg_original = (total_original_size / successful) if successful > 0 else 0
    avg_new = (total_new_size / successful) if successful > 0 else 0
    total_compression = ((total_original_size - total_new_size) / total_original_size * 100) if total_original_size > 0 else 0
    
    print(f"\n📊 {hsk_folder} Summary:")
    print(f"  ✅ Successful: {successful}")
    print(f"  ❌ Failed: {failed}")
    print(f"  📈 Average MP3 size: {avg_original/1024:.1f}KB")
    print(f"  📉 Average Opus size: {avg_new/1024:.1f}KB")
    print(f"  🎯 Compression: {total_compression:.1f}% smaller")
    print(f"  💾 Total saved: {(total_original_size - total_new_size)/1024/1024:.1f}MB")
    
    return successful, failed, total_original_size, total_new_size

def test_conversion() -> bool:
    """Test the conversion with a single file"""
    print("🔍 Testing Opus conversion...")
    
    # Find the first MP3 file to test
    test_file = None
    for folder in ["hsk1", "hsk2", "hsk3", "hsk4", "hsk5", "hsk6"]:
        if os.path.exists(folder):
            mp3_files = list(Path(folder).glob("*.mp3"))
            if mp3_files:
                test_file = mp3_files[0]
                break
    
    if not test_file:
        print("❌ No MP3 files found for testing")
        return False
    
    test_opus = "test_conversion.opus"
    success, filename, original_size, new_size = convert_mp3_to_opus(str(test_file), test_opus)
    
    if success:
        compression_ratio = ((original_size - new_size) / original_size) * 100
        print(f"✅ Test successful!")
        print(f"  📁 File: {filename}")
        print(f"  📈 Original: {original_size} bytes ({original_size/1024:.1f}KB)")
        print(f"  📉 Opus: {new_size} bytes ({new_size/1024:.1f}KB)")
        print(f"  🎯 Compression: {compression_ratio:.1f}% smaller")
        
        # Clean up test file
        os.remove(test_opus)
        return True
    else:
        print("❌ Test failed")
        return False

def main():
    """Main function to convert all HSK MP3 files to Opus"""
    print("🚀 HSK MP3 to Opus Converter")
    print("🎯 Optimized for Flutter apps with 2-4KB file sizes")
    print(f"🎵 Settings: {OPUS_CONFIG['bitrate']} bitrate, {OPUS_CONFIG['sample_rate']}Hz, {OPUS_CONFIG['application']} mode")
    
    # Test conversion first
    if not test_conversion():
        print("❌ Cannot proceed without working conversion")
        return
    
    # Find all HSK folders
    hsk_folders = []
    for folder in ["hsk1", "hsk2", "hsk3", "hsk4", "hsk5", "hsk6"]:
        if os.path.exists(folder) and os.path.isdir(folder):
            hsk_folders.append(folder)
    
    if not hsk_folders:
        print("❌ No HSK folders found!")
        return
    
    print(f"\n📁 Found HSK folders: {', '.join(hsk_folders)}")
    
    # Process each folder
    start_time = time.time()
    total_successful = 0
    total_failed = 0
    total_original_size = 0
    total_new_size = 0
    
    for folder in hsk_folders:
        folder_start = time.time()
        successful, failed, original_size, new_size = process_hsk_folder(folder)
        folder_time = time.time() - folder_start
        
        total_successful += successful
        total_failed += failed
        total_original_size += original_size
        total_new_size += new_size
        
        print(f"⏱️ {folder} completed in {folder_time:.1f} seconds")
    
    # Final summary
    total_time = time.time() - start_time
    total_compression = ((total_original_size - total_new_size) / total_original_size * 100) if total_original_size > 0 else 0
    avg_new_size = (total_new_size / total_successful) if total_successful > 0 else 0
    
    print(f"\n🎉 Conversion Complete!")
    print(f"⏱️ Total time: {total_time:.1f} seconds")
    print(f"✅ Total successful: {total_successful}")
    print(f"❌ Total failed: {total_failed}")
    print(f"📊 Average Opus file size: {avg_new_size/1024:.1f}KB")
    print(f"🎯 Total compression: {total_compression:.1f}% smaller")
    print(f"💾 Total space saved: {(total_original_size - total_new_size)/1024/1024:.1f}MB")
    print(f"\n💡 Opus files are ready for your Flutter app!")
    print(f"📱 Use audioplayers or just_audio packages for playback")

if __name__ == "__main__":
    main()
