import requests
import os

# Test the Verbatik API with the exact format from their documentation
API_KEY = "vbt_pDMUIEac9JnycAj2Ab5kHDC4XMTsuVkd"

url = 'https://api.verbatik.com/api/v1/tts'
headers = {
    'Content-Type': 'application/ssml+xml',
    'Authorization': f'Bearer {API_KEY}',
    'X-Voice-ID': 'Xiaoxiao'
}
data = '你好'  # Simple Chinese text

print("Testing Verbatik API...")
print(f"URL: {url}")
print(f"Headers: {headers}")
print(f"Data: {data}")

response = requests.post(url, headers=headers, data=data)

print(f"Status Code: {response.status_code}")
print(f"Response Headers: {dict(response.headers)}")

if response.status_code == 200:
    print("✅ Success! Saving audio file...")
    with open('test_output.mp3', 'wb') as f:
        f.write(response.content)
    
    file_size = os.path.getsize('test_output.mp3')
    print(f"📁 File saved: test_output.mp3 ({file_size} bytes / {file_size/1024:.1f}KB)")
else:
    print(f"❌ Error: {response.text}")
