#!/usr/bin/env python3
"""
Quick Chinese Voice Tester for ElevenLabs
This script helps you find the best native-sounding Chinese voice before processing all HSK files.
"""

import json
import os
import asyncio
import aiohttp
import aiofiles

# ElevenLabs API configuration
API_KEY = os.getenv("ELEVENLABS_API_KEY", "***************************************************")
BASE_URL = "https://api.elevenlabs.io/v1"

# Selected voice (Amy - user-tested)
SELECTED_VOICE_ID = "bhJUNIXWQQ94l8eI2VUf"
SELECTED_VOICE_NAME = "Amy"

async def get_available_voices(session: aiohttp.ClientSession) -> dict:
    """Fetch available voices from ElevenLabs API"""
    headers = {
        "Accept": "application/json",
        "xi-api-key": API_KEY
    }
    
    try:
        async with session.get(f"{BASE_URL}/voices", headers=headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_text = await response.text()
                print(f"Failed to fetch voices: {response.status}")
                print(f"Error details: {error_text}")
                if response.status == 401:
                    print("❌ Authentication failed! Please check your API key.")
                    print("💡 Make sure your ElevenLabs API key is valid and has sufficient credits.")
                return {}
    except Exception as e:
        print(f"Error fetching voices: {e}")
        return {}

async def generate_test_audio(session: aiohttp.ClientSession, text: str, voice_id: str, output_path: str) -> bool:
    """Generate test audio using ElevenLabs API"""
    url = f"{BASE_URL}/text-to-speech/{voice_id}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_turbo_v2_5",
        "voice_settings": {
            "stability": 0.7,
            "similarity_boost": 0.8,
            "style": 0.2,
            "use_speaker_boost": True
        },
        "output_format": "mp3_22050_32"
    }
    
    try:
        async with session.post(url, json=data, headers=headers) as response:
            if response.status == 200:
                async with aiofiles.open(output_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                return True
            else:
                error_text = await response.text()
                print(f"API Error {response.status}: {error_text}")
                return False
    except Exception as e:
        print(f"Error generating audio: {e}")
        return False

async def test_voice(session: aiohttp.ClientSession, voice_id: str, voice_name: str):
    """Test a single voice with Chinese text"""
    test_text = "你好，我叫小明。今天天气很好，我们一起学习中文吧！"
    output_path = f"test_{voice_name.replace(' ', '_')}_{voice_id[:8]}.mp3"
    
    print(f"Testing: {voice_name} ({voice_id})")
    success = await generate_test_audio(session, test_text, voice_id, output_path)
    
    if success:
        file_size = os.path.getsize(output_path)
        print(f"✅ Generated: {output_path} ({file_size} bytes)")
        return output_path
    else:
        print(f"❌ Failed to generate audio for {voice_name}")
        return None

async def main():
    """Main function to test Chinese voices"""
    print("🎤 ElevenLabs Chinese Voice Tester")
    print("=" * 50)
    
    # Validate API key
    if not API_KEY or len(API_KEY) < 20:
        print("❌ Invalid or missing API key!")
        print("💡 Set your ElevenLabs API key as environment variable:")
        print("   export ELEVENLABS_API_KEY=your_key_here")
        return
    
    async with aiohttp.ClientSession() as session:
        # Get all available voices
        print("🔍 Fetching available voices...")
        voices_data = await get_available_voices(session)
        
        if not voices_data or 'voices' not in voices_data:
            print("❌ Could not fetch voice data")
            return
        
        # Find Chinese voices
        chinese_voices = []
        for voice in voices_data['voices']:
            voice_id = voice.get('voice_id', '')
            name = voice.get('name', '')
            labels = voice.get('labels', {})
            description = voice.get('description', '')
            
            # Look for Chinese indicators
            all_text = f"{name} {description} {' '.join(str(v) for v in labels.values())}".lower()
            chinese_indicators = ['chinese', 'mandarin', 'zh-cn', 'zh-tw', '中文', '普通话']
            
            if any(indicator in all_text for indicator in chinese_indicators):
                chinese_voices.append({
                    'voice_id': voice_id,
                    'name': name,
                    'labels': labels,
                    'description': description
                })
        
        print(f"🎯 Found {len(chinese_voices)} Chinese voices")
        print("\nGenerating test audio files...")
        
        # Test each Chinese voice
        test_files = []
        for voice in chinese_voices[:10]:  # Limit to first 10 to avoid too many files
            test_file = await test_voice(session, voice['voice_id'], voice['name'])
            if test_file:
                test_files.append((test_file, voice))
        
        print(f"\n🔊 Generated {len(test_files)} test audio files")
        print("\n" + "=" * 50)
        print("INSTRUCTIONS:")
        print("1. Play each test audio file")
        print("2. Listen for native Chinese pronunciation")
        print("3. Note the voice ID of the best-sounding voice")
        print("4. Use that voice ID in your main script")
        print("=" * 50)
        
        print("\nTest files generated:")
        for test_file, voice in test_files:
            print(f"🎵 {test_file}")
            print(f"   Voice: {voice['name']} (ID: {voice['voice_id']})")
            print(f"   Description: {voice.get('description', 'N/A')}")
            print()

if __name__ == "__main__":
    asyncio.run(main())
