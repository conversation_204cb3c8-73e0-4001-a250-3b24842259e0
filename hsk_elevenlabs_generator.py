import json
import os
import asyncio
import aiohttp
import aiofiles
from typing import Optional

# ElevenLabs API configuration
API_KEY = os.getenv("ELEVENLABS_API_KEY", "***************************************************")  # Use environment variable or fallback
BASE_URL = "https://api.elevenlabs.io/v1"

# Selected native-sounding Chinese voice (user-tested)
SELECTED_CHINESE_VOICE = "bhJUNIXWQQ94l8eI2VUf"  # Amy - User-selected native-sounding voice
SELECTED_VOICE_NAME = "Amy"

# Backup voices (in case the selected voice fails)
BACKUP_VOICES = [
    "21m00Tcm4TlvDq8ikWAM",  # Rachel - Professional, clear (multilingual)
    "pMsXgVXv3BLzUgSXRplE",  # Amy - Natural, friendly (multilingual)
]

# Last resort fallback
FALLBACK_VOICE = "21m00Tcm4TlvDq8ikWAM"

async def get_available_voices(session: aiohttp.ClientSession) -> dict:
    """Fetch available voices from ElevenLabs API"""
    headers = {
        "Accept": "application/json",
        "xi-api-key": API_KEY
    }
    
    try:
        async with session.get(f"{BASE_URL}/voices", headers=headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                print(f"Failed to fetch voices: {response.status}")
                return {}
    except Exception as e:
        print(f"Error fetching voices: {e}")
        return {}

def find_best_chinese_voice(voices_data: dict) -> str:
    """Find the best NATIVE Chinese voice from available voices"""
    if not voices_data or 'voices' not in voices_data:
        print("❌ No voice data available, using fallback")
        return FALLBACK_VOICE

    print(f"🔍 Analyzing {len(voices_data.get('voices', []))} available voices...")

    native_chinese = []
    multilingual_chinese = []

    for voice in voices_data['voices']:
        voice_id = voice.get('voice_id', '')
        name = voice.get('name', '')
        labels = voice.get('labels', {})
        description = voice.get('description', '')

        # Convert all text to lowercase for comparison
        all_text = f"{name} {description} {' '.join(str(v) for v in labels.values())}".lower()

        # Look for native Chinese indicators
        native_indicators = ['native chinese', 'mandarin native', 'chinese native', 'beijing', 'shanghai', 'guangzhou']
        chinese_indicators = ['chinese', 'mandarin', 'zh-cn', 'zh-tw', '中文', '普通话']

        is_native = any(indicator in all_text for indicator in native_indicators)
        is_chinese = any(indicator in all_text for indicator in chinese_indicators)

        if is_native:
            native_chinese.append({
                'voice_id': voice_id,
                'name': name,
                'labels': labels,
                'description': description,
                'priority': 1  # Highest priority
            })
            print(f"🎯 Found NATIVE Chinese voice: {name} ({voice_id})")

        elif is_chinese:
            multilingual_chinese.append({
                'voice_id': voice_id,
                'name': name,
                'labels': labels,
                'description': description,
                'priority': 2  # Lower priority
            })
            print(f"🌐 Found multilingual Chinese voice: {name} ({voice_id})")

    # Prefer native Chinese voices first
    if native_chinese:
        # Sort by female preference
        for voice in native_chinese:
            labels_text = ' '.join(str(v) for v in voice['labels'].values()).lower()
            if 'female' in labels_text or 'woman' in labels_text:
                print(f"✅ Selected NATIVE Chinese female voice: {voice['name']} ({voice['voice_id']})")
                return voice['voice_id']

        # If no female native voice, use first native voice
        selected = native_chinese[0]
        print(f"✅ Selected NATIVE Chinese voice: {selected['name']} ({selected['voice_id']})")
        return selected['voice_id']

    # Fallback to multilingual Chinese voices
    if multilingual_chinese:
        for voice in multilingual_chinese:
            labels_text = ' '.join(str(v) for v in voice['labels'].values()).lower()
            if 'female' in labels_text:
                print(f"⚠️ Using multilingual Chinese female voice: {voice['name']} ({voice['voice_id']})")
                return voice['voice_id']

        selected = multilingual_chinese[0]
        print(f"⚠️ Using multilingual Chinese voice: {selected['name']} ({selected['voice_id']})")
        return selected['voice_id']

    # Last resort - try multilingual voices
    print("⚠️ No native Chinese voices found, trying multilingual voices...")
    for voice_id in MULTILINGUAL_VOICES:
        print(f"🔄 Trying multilingual voice: {voice_id}")
        return voice_id

    print("❌ Using fallback voice - may not sound native")
    return FALLBACK_VOICE

async def generate_audio_elevenlabs(session: aiohttp.ClientSession, text: str, voice_id: str, output_path: str) -> bool:
    """Generate audio using ElevenLabs API"""
    url = f"{BASE_URL}/text-to-speech/{voice_id}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_turbo_v2_5",  # Faster and better for Chinese pronunciation
        "voice_settings": {
            "stability": 0.7,        # Higher stability for clearer Chinese pronunciation
            "similarity_boost": 0.8,  # Higher similarity for more authentic voice
            "style": 0.2,            # Slight style for more natural speech
            "use_speaker_boost": True
        },
        "output_format": "mp3_22050_32"  # Optimized for smaller file size while maintaining quality
    }
    
    try:
        async with session.post(url, json=data, headers=headers) as response:
            if response.status == 200:
                # Save the audio file
                async with aiofiles.open(output_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                return True
            else:
                error_text = await response.text()
                print(f"API Error {response.status}: {error_text}")
                return False
                
    except Exception as e:
        print(f"Error generating audio: {e}")
        return False

async def test_voice_quality(session: aiohttp.ClientSession, voice_id: str) -> bool:
    """Test the selected voice with a sample Chinese text"""
    test_text = "你好，这是一个测试。我是中文语音助手。"  # "Hello, this is a test. I am a Chinese voice assistant."
    test_path = "voice_test.mp3"

    print(f"🎤 Testing voice quality with: '{test_text}'")
    success = await generate_audio_elevenlabs(session, test_text, voice_id, test_path)

    if success and os.path.exists(test_path):
        file_size = os.path.getsize(test_path)
        print(f"✅ Voice test successful! Generated {file_size} bytes")
        print(f"🔊 Please play '{test_path}' to verify the voice sounds like a native Chinese speaker")

        # Ask user for confirmation
        response = input("Does this voice sound like a native Chinese speaker? (y/n): ").lower().strip()

        # Clean up test file
        try:
            os.remove(test_path)
        except:
            pass

        return response.startswith('y')
    else:
        print("❌ Voice test failed!")
        return False

async def process_hsk_level(session: aiohttp.ClientSession, level: int, voice_id: str):
    """Process all characters for a specific HSK level"""
    print(f"Processing HSK level {level}...")
    
    # Create directory for this level
    os.makedirs(f'HSK{level}', exist_ok=True)
    
    # Load JSON data
    json_path = f'hsk-json-all/hsk-level-{level}.json'
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
    except FileNotFoundError:
        print(f"❌ JSON file not found: {json_path}")
        return
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON file: {json_path}")
        return
    
    successful = 0
    failed = 0
    
    # Generate audio for each character
    for i, char_data in enumerate(characters):
        char_id = char_data.get('id', f"unknown_{i}")
        hanzi = char_data.get('hanzi', '')
        pinyin = char_data.get('pinyin', '')
        
        if not hanzi:
            print(f"⚠️ Skipping entry {char_id}: No hanzi found")
            continue
        
        # Output path
        output_path = f'HSK{level}/{char_id}.mp3'
        
        # Skip if file already exists
        if os.path.exists(output_path):
            print(f"⏭️ Skipping {hanzi} (ID: {char_id}), file already exists")
            continue
        
        print(f"🎵 Generating audio for {hanzi} (ID: {char_id}, Pinyin: {pinyin})")
        
        # Generate audio
        success = await generate_audio_elevenlabs(session, hanzi, voice_id, output_path)
        
        if success:
            # Check file size
            file_size = os.path.getsize(output_path)
            print(f"✅ Created audio for {hanzi} ({file_size} bytes)")
            successful += 1
        else:
            print(f"❌ Failed to generate audio for {hanzi}")
            failed += 1
        
        # Rate limiting: small delay between requests
        await asyncio.sleep(0.1)
    
    print(f"HSK Level {level} completed: {successful} successful, {failed} failed")

async def test_voice_quality(session: aiohttp.ClientSession, voice_id: str, voice_name: str) -> bool:
    """Test the selected voice with sample Chinese text"""
    test_texts = [
        "你好，我是中文语音助手。",  # Hello, I am a Chinese voice assistant
        "这是一个测试，请仔细听发音。",  # This is a test, please listen carefully to the pronunciation
        "学习中文很有趣。"  # Learning Chinese is very interesting
    ]

    print(f"\n🎤 Testing voice: {voice_name} ({voice_id})")

    for i, test_text in enumerate(test_texts):
        test_path = f"voice_test_{i+1}.mp3"
        print(f"   Generating: '{test_text}'")

        success = await generate_audio_elevenlabs(session, test_text, voice_id, test_path)

        if success and os.path.exists(test_path):
            file_size = os.path.getsize(test_path)
            print(f"   ✅ Generated {file_size} bytes -> {test_path}")
        else:
            print(f"   ❌ Failed to generate test audio")
            return False

    print(f"\n🔊 Please play the test files (voice_test_1.mp3, voice_test_2.mp3, voice_test_3.mp3)")
    print(f"   Listen carefully to check if this sounds like a NATIVE Chinese speaker")

    while True:
        response = input(f"\nDoes '{voice_name}' sound like a native Chinese speaker? (y/n/skip): ").lower().strip()
        if response in ['y', 'yes']:
            # Clean up test files
            for i in range(3):
                try:
                    os.remove(f"voice_test_{i+1}.mp3")
                except:
                    pass
            return True
        elif response in ['n', 'no']:
            # Clean up test files
            for i in range(3):
                try:
                    os.remove(f"voice_test_{i+1}.mp3")
                except:
                    pass
            return False
        elif response in ['skip', 's']:
            return False
        else:
            print("Please enter 'y' for yes, 'n' for no, or 'skip' to try next voice")

async def main():
    """Main function to process all HSK levels"""
    print("🚀 Starting HSK audio generation with ElevenLabs...")
    print("⚠️  Remember to keep your API key secure!")

    # Validate API key
    if not API_KEY or API_KEY.startswith("sk_") and len(API_KEY) < 20:
        print("❌ Invalid or missing API key!")
        print("💡 Set your ElevenLabs API key as environment variable: export ELEVENLABS_API_KEY=your_key_here")
        return
    
    # Create aiohttp session
    connector = aiohttp.TCPConnector(limit=10)  # Limit concurrent connections
    async with aiohttp.ClientSession(connector=connector) as session:
        
        # Use the pre-selected Amy voice (user-tested)
        print(f"🎤 Using pre-selected voice: {SELECTED_VOICE_NAME} ({SELECTED_CHINESE_VOICE})")

        # Test the selected voice to make sure it works
        print("🔍 Testing selected voice...")
        voice_works = await test_voice_quality(session, SELECTED_CHINESE_VOICE, SELECTED_VOICE_NAME)

        if voice_works:
            voice_id = SELECTED_CHINESE_VOICE
            voice_name = SELECTED_VOICE_NAME
            print(f"✅ Voice test successful!")
        else:
            print("⚠️ Selected voice failed. Trying backup voices...")
            voice_id = None
            voice_name = None

            # Try backup voices
            for backup_id in BACKUP_VOICES:
                print(f"🔄 Testing backup voice: {backup_id}")
                if await test_voice_quality(session, backup_id, f"Backup-{backup_id[:8]}"):
                    voice_id = backup_id
                    voice_name = f"Backup-{backup_id[:8]}"
                    break

            if not voice_id:
                print("❌ All voices failed. Using fallback.")
                voice_id = FALLBACK_VOICE
                voice_name = "Fallback Voice"

        # Confirm before processing all files
        print(f"\n🎤 Final voice selection: {voice_name} ({voice_id})")
        confirm = input("Proceed with processing all HSK levels? (y/n): ").lower().strip()

        if not confirm.startswith('y'):
            print("❌ Processing cancelled by user.")
            return

        # Process each HSK level (1-6)
        print("\n🚀 Starting HSK audio generation...")
        for level in range(1, 7):
            await process_hsk_level(session, level, voice_id)

        print("🎉 All HSK levels processed!")
        print("💡 Check the generated folders: HSK1/, HSK2/, HSK3/, HSK4/, HSK5/, HSK6/")

# Run the async main function
if __name__ == "__main__":
    asyncio.run(main())