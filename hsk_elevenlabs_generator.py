import json
import os
import asyncio
import aiohttp
import aiofiles
from typing import Optional

# ElevenLabs API configuration
API_KEY = os.getenv("ELEVENLABS_API_KEY", "***************************************************")  # Use environment variable or fallback
BASE_URL = "https://api.elevenlabs.io/v1"

# Best Chinese female voices for educational content
# We'll try these in order of preference
PREFERRED_VOICES = [
    "pMsXgVXv3BLzUgSXRplE",  # Amy - Natural, friendly female voice
    "Xb7hH8MSUJpSbSDYk0k2",  # Alice - Clear, educational tone
    "21m00Tcm4TlvDq8ikWAM",  # Rachel - Professional, clear
    "AZnzlk1XvdvUeBnXmlld",  # Domi - Confident, clear
]

# Fallback to a general Chinese voice ID if the above don't work
FALLBACK_VOICE = "21m00Tcm4TlvDq8ikWAM"  # Rachel (multilingual)

async def get_available_voices(session: aiohttp.ClientSession) -> dict:
    """Fetch available voices from ElevenLabs API"""
    headers = {
        "Accept": "application/json",
        "xi-api-key": API_KEY
    }
    
    try:
        async with session.get(f"{BASE_URL}/voices", headers=headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                print(f"Failed to fetch voices: {response.status}")
                return {}
    except Exception as e:
        print(f"Error fetching voices: {e}")
        return {}

def find_best_chinese_voice(voices_data: dict) -> str:
    """Find the best Chinese female voice from available voices"""
    if not voices_data or 'voices' not in voices_data:
        print("Using fallback voice ID")
        return FALLBACK_VOICE
    
    # Look for Chinese voices
    chinese_voices = []
    for voice in voices_data['voices']:
        voice_id = voice.get('voice_id', '')
        name = voice.get('name', '')
        labels = voice.get('labels', {})
        
        # Check if it's a Chinese voice
        if any(label.lower() in ['chinese', 'mandarin', 'zh'] for label in labels.values() if isinstance(label, str)):
            chinese_voices.append({
                'voice_id': voice_id,
                'name': name,
                'labels': labels
            })
            print(f"Found Chinese voice: {name} ({voice_id})")
    
    # If we found Chinese voices, prefer female ones
    for voice in chinese_voices:
        labels = voice.get('labels', {})
        if any('female' in str(label).lower() for label in labels.values()):
            print(f"Selected Chinese female voice: {voice['name']} ({voice['voice_id']})")
            return voice['voice_id']
    
    # If no specific Chinese female voice found, use the first Chinese voice
    if chinese_voices:
        selected = chinese_voices[0]
        print(f"Selected Chinese voice: {selected['name']} ({selected['voice_id']})")
        return selected['voice_id']
    
    # Fallback to trying our preferred voices
    for voice_id in PREFERRED_VOICES:
        print(f"Trying preferred voice: {voice_id}")
        return voice_id
    
    print("Using fallback voice")
    return FALLBACK_VOICE

async def generate_audio_elevenlabs(session: aiohttp.ClientSession, text: str, voice_id: str, output_path: str) -> bool:
    """Generate audio using ElevenLabs API"""
    url = f"{BASE_URL}/text-to-speech/{voice_id}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_multilingual_v2",  # Best model for Chinese
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.75,
            "style": 0.0,
            "use_speaker_boost": True
        },
        "output_format": "mp3_22050_32"  # Optimized for smaller file size while maintaining quality
    }
    
    try:
        async with session.post(url, json=data, headers=headers) as response:
            if response.status == 200:
                # Save the audio file
                async with aiofiles.open(output_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                return True
            else:
                error_text = await response.text()
                print(f"API Error {response.status}: {error_text}")
                return False
                
    except Exception as e:
        print(f"Error generating audio: {e}")
        return False

async def process_hsk_level(session: aiohttp.ClientSession, level: int, voice_id: str):
    """Process all characters for a specific HSK level"""
    print(f"Processing HSK level {level}...")
    
    # Create directory for this level
    os.makedirs(f'HSK{level}', exist_ok=True)
    
    # Load JSON data
    json_path = f'hsk-json-all/hsk-level-{level}.json'
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
    except FileNotFoundError:
        print(f"❌ JSON file not found: {json_path}")
        return
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON file: {json_path}")
        return
    
    successful = 0
    failed = 0
    
    # Generate audio for each character
    for i, char_data in enumerate(characters):
        char_id = char_data.get('id', f"unknown_{i}")
        hanzi = char_data.get('hanzi', '')
        pinyin = char_data.get('pinyin', '')
        
        if not hanzi:
            print(f"⚠️ Skipping entry {char_id}: No hanzi found")
            continue
        
        # Output path
        output_path = f'HSK{level}/{char_id}.mp3'
        
        # Skip if file already exists
        if os.path.exists(output_path):
            print(f"⏭️ Skipping {hanzi} (ID: {char_id}), file already exists")
            continue
        
        print(f"🎵 Generating audio for {hanzi} (ID: {char_id}, Pinyin: {pinyin})")
        
        # Generate audio
        success = await generate_audio_elevenlabs(session, hanzi, voice_id, output_path)
        
        if success:
            # Check file size
            file_size = os.path.getsize(output_path)
            print(f"✅ Created audio for {hanzi} ({file_size} bytes)")
            successful += 1
        else:
            print(f"❌ Failed to generate audio for {hanzi}")
            failed += 1
        
        # Rate limiting: small delay between requests
        await asyncio.sleep(0.1)
    
    print(f"HSK Level {level} completed: {successful} successful, {failed} failed")

async def main():
    """Main function to process all HSK levels"""
    print("🚀 Starting HSK audio generation with ElevenLabs...")
    print("⚠️  Remember to keep your API key secure!")

    # Validate API key
    if not API_KEY or API_KEY.startswith("sk_") and len(API_KEY) < 20:
        print("❌ Invalid or missing API key!")
        print("💡 Set your ElevenLabs API key as environment variable: export ELEVENLABS_API_KEY=your_key_here")
        return
    
    # Create aiohttp session
    connector = aiohttp.TCPConnector(limit=10)  # Limit concurrent connections
    async with aiohttp.ClientSession(connector=connector) as session:
        
        # Get available voices and select the best Chinese one
        print("🔍 Fetching available voices...")
        voices_data = await get_available_voices(session)
        voice_id = find_best_chinese_voice(voices_data)
        
        print(f"🎤 Using voice ID: {voice_id}")
        
        # Process each HSK level (1-6)
        total_successful = 0
        total_failed = 0
        
        for level in range(1, 7):
            await process_hsk_level(session, level, voice_id)
        
        print("🎉 All HSK levels processed!")
        print("💡 Tip: Check the file sizes - they should be much smaller than before!")

# Run the async main function
if __name__ == "__main__":
    asyncio.run(main())