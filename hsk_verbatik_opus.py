import json
import os
import requests
import subprocess
import time
from pathlib import Path

# Verbatik API configuration
API_KEY = os.getenv("VERBATIK_API_KEY", "vbt_pDMUIEac9JnycAj2Ab5kHDC4XMTsuVkd")
BASE_URL = "https://api.verbatik.com/api/v1/tts"

# Audio format configuration for optimal Flutter compatibility
AUDIO_CONFIG = {
    "format": "opus",           # Best format for speech and small files
    "extension": ".opus",       # File extension
    "bitrate": "16k",          # Low bitrate for 2-4KB target (perfect for speech)
    "sample_rate": "16000",    # 16kHz is perfect for speech recognition and TTS
    "channels": "1"            # Mono for smaller files (speech doesn't need stereo)
}

def generate_audio_verbatik(text: str, output_path: str) -> bool:
    """Generate audio using Verbatik API with Xiaoxiao voice"""
    
    headers = {
        'Content-Type': 'application/ssml+xml',
        'Authorization': f'Bearer {API_KEY}',
        'X-Voice-ID': 'Xiaoxiao'  # Best female Chinese voice for educational content
    }
    
    try:
        response = requests.post(BASE_URL, headers=headers, data=text, timeout=30)
        
        if response.status_code == 200:
            # Save the MP3 file temporarily
            temp_mp3 = output_path.replace(AUDIO_CONFIG["extension"], ".mp3")
            with open(temp_mp3, 'wb') as f:
                f.write(response.content)
            return temp_mp3
        else:
            print(f"API Error {response.status_code}: {response.text}")
            return None
            
    except Exception as e:
        print(f"Error generating audio: {e}")
        return None

def convert_to_opus(mp3_path: str, opus_path: str) -> bool:
    """Convert MP3 to Opus format optimized for small file size and speech quality"""
    
    try:
        # FFmpeg command optimized for speech and small file size
        cmd = [
            'ffmpeg',
            '-i', mp3_path,                                    # Input MP3 file
            '-c:a', 'libopus',                                # Opus codec
            '-b:a', AUDIO_CONFIG["bitrate"],                  # Bitrate (16k for small files)
            '-ar', AUDIO_CONFIG["sample_rate"],               # Sample rate (16kHz for speech)
            '-ac', AUDIO_CONFIG["channels"],                  # Mono channel
            '-application', 'voip',                           # Optimize for speech (VoIP mode)
            '-frame_duration', '60',                          # 60ms frames for better compression
            '-packet_loss', '1',                              # Optimize for potential packet loss
            '-y',                                             # Overwrite output file
            opus_path
        ]
        
        # Run FFmpeg conversion
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Remove temporary MP3 file
            os.remove(mp3_path)
            return True
        else:
            print(f"FFmpeg error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Error converting to Opus: {e}")
        return False

def process_hsk_level(level: int):
    """Process all characters for a specific HSK level"""
    print(f"Processing HSK level {level}...")
    
    # Create directory for this level
    os.makedirs(f'HSK{level}', exist_ok=True)
    
    # Load JSON data
    json_path = f'hsk-json-all/hsk-level-{level}.json'
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
    except FileNotFoundError:
        print(f"❌ JSON file not found: {json_path}")
        return
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON file: {json_path}")
        return
    
    successful = 0
    failed = 0
    total_size = 0
    
    # Generate audio for each character
    for i, char_data in enumerate(characters):
        char_id = char_data.get('id', f"unknown_{i}")
        hanzi = char_data.get('hanzi', '')
        pinyin = char_data.get('pinyin', '')
        
        if not hanzi:
            print(f"⚠️ Skipping entry {char_id}: No hanzi found")
            continue
        
        # Output path with Opus extension
        output_path = f'HSK{level}/{char_id}{AUDIO_CONFIG["extension"]}'
        
        # Skip if file already exists
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"⏭️ Skipping {hanzi} (ID: {char_id}), file already exists ({file_size} bytes)")
            total_size += file_size
            successful += 1
            continue
        
        print(f"🎵 Generating audio for {hanzi} (ID: {char_id}, Pinyin: {pinyin})")
        
        # Step 1: Generate MP3 from Verbatik
        temp_mp3 = generate_audio_verbatik(hanzi, output_path)
        
        if temp_mp3:
            # Step 2: Convert MP3 to Opus
            conversion_success = convert_to_opus(temp_mp3, output_path)
            
            if conversion_success and os.path.exists(output_path):
                # Check final file size
                file_size = os.path.getsize(output_path)
                total_size += file_size
                size_kb = file_size / 1024
                
                if size_kb <= 2:
                    print(f"🎯 Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Perfect size!")
                elif size_kb <= 4:
                    print(f"✅ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Target achieved!")
                elif size_kb <= 6:
                    print(f"✅ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Good size")
                else:
                    print(f"⚠️ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Large file")
                
                successful += 1
            else:
                print(f"❌ Failed to convert audio for {hanzi}")
                failed += 1
        else:
            print(f"❌ Failed to generate audio for {hanzi}")
            failed += 1
        
        # Rate limiting: small delay between requests to be respectful
        time.sleep(0.2)
    
    avg_size = (total_size / successful) if successful > 0 else 0
    avg_size_kb = avg_size / 1024
    
    print(f"HSK Level {level} completed:")
    print(f"  ✅ Successful: {successful}")
    print(f"  ❌ Failed: {failed}")
    print(f"  📊 Average file size: {avg_size:.0f} bytes ({avg_size_kb:.1f}KB)")
    print(f"  💾 Total size: {total_size/1024:.1f}KB")
    print(f"  🎯 Format: {AUDIO_CONFIG['format'].upper()} @ {AUDIO_CONFIG['bitrate']} bitrate")

def test_conversion_pipeline() -> bool:
    """Test the complete pipeline: Verbatik API -> MP3 -> Opus conversion"""
    print("🔍 Testing complete audio pipeline...")
    
    test_text = "测试"
    temp_mp3 = "test_pipeline.mp3"
    test_opus = "test_pipeline.opus"
    
    # Step 1: Test Verbatik API
    headers = {
        'Content-Type': 'application/ssml+xml',
        'Authorization': f'Bearer {API_KEY}',
        'X-Voice-ID': 'Xiaoxiao'
    }
    
    try:
        response = requests.post(BASE_URL, headers=headers, data=test_text, timeout=30)
        if response.status_code == 200:
            with open(temp_mp3, 'wb') as f:
                f.write(response.content)
            mp3_size = os.path.getsize(temp_mp3)
            print(f"✅ Verbatik API test successful: {mp3_size} bytes MP3")
        else:
            print(f"❌ Verbatik API test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Verbatik API test error: {e}")
        return False
    
    # Step 2: Test Opus conversion
    conversion_success = convert_to_opus(temp_mp3, test_opus)
    
    if conversion_success and os.path.exists(test_opus):
        opus_size = os.path.getsize(test_opus)
        compression_ratio = (mp3_size - opus_size) / mp3_size * 100
        print(f"✅ Opus conversion successful: {opus_size} bytes ({opus_size/1024:.1f}KB)")
        print(f"📊 Compression: {compression_ratio:.1f}% smaller than MP3")
        
        # Clean up test files
        os.remove(test_opus)
        return True
    else:
        print("❌ Opus conversion failed")
        # Clean up MP3 if it still exists
        if os.path.exists(temp_mp3):
            os.remove(temp_mp3)
        return False

def main():
    """Main function to process all HSK levels with Opus conversion"""
    print("🚀 Starting HSK audio generation with Verbatik + Opus conversion...")
    print("🎯 Optimized for Flutter apps with 2-4KB file sizes")
    print(f"🎵 Audio format: {AUDIO_CONFIG['format'].upper()} @ {AUDIO_CONFIG['bitrate']} bitrate")
    
    # Validate API key
    if not API_KEY or len(API_KEY) < 20:
        print("❌ Invalid or missing API key!")
        print("💡 Set your Verbatik API key as environment variable: export VERBATIK_API_KEY=your_key_here")
        return
    
    # Test complete pipeline first
    if not test_conversion_pipeline():
        print("❌ Cannot proceed without working conversion pipeline")
        return
    
    print(f"🎤 Using Xiaoxiao voice with Opus compression for Flutter")
    
    # Process each HSK level (1-6)
    start_time = time.time()
    
    for level in range(1, 7):
        level_start = time.time()
        process_hsk_level(level)
        level_time = time.time() - level_start
        print(f"⏱️ HSK{level} completed in {level_time:.1f} seconds\n")
    
    total_time = time.time() - start_time
    print("🎉 All HSK levels processed!")
    print(f"⏱️ Total time: {total_time:.1f} seconds")
    print("💡 Files optimized for Flutter with clear Chinese tones and minimal footprint!")

# Run the main function
if __name__ == "__main__":
    main()
