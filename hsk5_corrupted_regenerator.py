#!/usr/bin/env python3
"""
HSK5 Corrupted Files Regenerator
Regenerates the final 28 corrupted HSK5 MP3 files using Microsoft EdgeTTS.
These files failed during MP3 to Opus conversion due to MPEG frame corruption.
"""

import json
import os
import asyncio
import edge_tts
import time

# Define the voice to use - <PERSON><PERSON><PERSON><PERSON> is a high-quality female Mandarin voice
VOICE = "zh-CN-XiaoXiaoNeural"

# The exact 28 failed HSK5 file IDs from conversion output
FAILED_HSK5_IDS = [
    1370, 1204, 1367, 1821, 1500, 2424, 2181, 1473, 2024, 2144,
    1896, 1468, 2001, 1246, 1293, 1683, 1725, 1309, 1452, 2198,
    1849, 1966, 1594, 2465, 2471, 2075, 1961, 1369
]

async def generate_audio(text, output_path):
    """Generate audio file using edge-tts with the specified voice"""
    try:
        communicate = edge_tts.Communicate(text, VOICE)
        await communicate.save(output_path)
        # Small delay to avoid rate limiting
        await asyncio.sleep(0.1)
        return True
    except Exception as e:
        print(f"❌ EdgeTTS error: {e}")
        return False

def load_character_data(char_id):
    """Load character data from HSK5 JSON file"""
    json_path = 'hsk-json-all/hsk-level-5.json'
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
        
        # Find character by ID
        for char_data in characters:
            if char_data.get('id') == char_id:
                hanzi = char_data.get('hanzi', '')
                pinyin = char_data.get('pinyin', '')
                return hanzi, pinyin
        
        print(f"⚠️ Character ID {char_id} not found in HSK5")
        return "", ""
        
    except Exception as e:
        print(f"❌ Error loading HSK5 data: {e}")
        return "", ""

async def test_edgetts_connection():
    """Test EdgeTTS connection with a simple request"""
    print("🔍 Testing EdgeTTS connection...")
    
    test_output = "test_hsk5_regen.mp3"
    success = await generate_audio("测试", test_output)
    
    if success and os.path.exists(test_output):
        file_size = os.path.getsize(test_output)
        print(f"✅ EdgeTTS test successful! ({file_size} bytes)")
        os.remove(test_output)  # Clean up test file
        return True
    else:
        print("❌ EdgeTTS test failed!")
        return False

async def regenerate_corrupted_hsk5_files():
    """Regenerate the 28 corrupted HSK5 files"""
    print("🚀 HSK5 Corrupted Files Regenerator")
    print("🎯 Target: 28 corrupted HSK5 files")
    print(f"🎤 Using Microsoft EdgeTTS with {VOICE}")
    
    # Test EdgeTTS connection first
    if not await test_edgetts_connection():
        print("❌ Cannot proceed without working EdgeTTS connection")
        return
    
    # Ensure HSK5 directory exists
    os.makedirs('hsk5', exist_ok=True)
    
    print(f"📊 Processing {len(FAILED_HSK5_IDS)} corrupted files...")
    
    successful = 0
    failed = 0
    total_size = 0
    start_time = time.time()
    
    for i, char_id in enumerate(FAILED_HSK5_IDS, 1):
        print(f"\n📥 [{i}/{len(FAILED_HSK5_IDS)}] Processing ID {char_id}...")
        
        # Load character data
        hanzi, pinyin = load_character_data(char_id)
        if not hanzi:
            print(f"⚠️ Skipping ID {char_id}: No character data found")
            failed += 1
            continue
        
        # Output path
        output_path = f'hsk5/{char_id}.mp3'
        
        # Check if file already exists and is valid
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            if file_size > 1000:  # If file is larger than 1KB, assume it's valid
                print(f"⏭️ Skipping '{hanzi}' (ID: {char_id}), valid file already exists ({file_size} bytes)")
                successful += 1
                total_size += file_size
                continue
            else:
                print(f"🔄 Replacing corrupted file for '{hanzi}' (ID: {char_id})")
        
        print(f"🎵 Generating audio for '{hanzi}' (拼音: {pinyin})")
        
        try:
            # Generate audio using EdgeTTS
            success = await generate_audio(hanzi, output_path)
            
            if success and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                total_size += file_size
                size_kb = file_size / 1024
                
                print(f"✅ Created audio for '{hanzi}' ({file_size} bytes / {size_kb:.1f}KB)")
                successful += 1
            else:
                print(f"❌ Failed to generate audio for '{hanzi}'")
                failed += 1
                
        except Exception as e:
            print(f"❌ Error generating audio for '{hanzi}': {e}")
            failed += 1
    
    # Summary
    total_time = time.time() - start_time
    avg_size = (total_size / successful) if successful > 0 else 0
    avg_size_kb = avg_size / 1024
    
    print(f"\n🎉 HSK5 Corrupted Files Regeneration Complete!")
    print(f"⏱️ Total time: {total_time:.1f} seconds")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Average file size: {avg_size:.0f} bytes ({avg_size_kb:.1f}KB)")
    print(f"💾 Total size: {total_size/1024:.1f}KB")
    
    if successful == len(FAILED_HSK5_IDS):
        print("🏆 Perfect! All 28 corrupted HSK5 files have been successfully regenerated!")
        print("💡 Next step: Convert these new MP3 files to Opus format")
        print("📁 Files are ready in: hsk5/")
        return True
    else:
        remaining = len(FAILED_HSK5_IDS) - successful
        print(f"⚠️ {remaining} files still need attention")
        return False

async def main():
    """Main function"""
    success = await regenerate_corrupted_hsk5_files()
    
    if success:
        print("\n🎯 Ready for Opus conversion!")
        print("Run: python3 convert_mp3_to_opus.py")
        print("This will convert only the new MP3 files to Opus format.")
    else:
        print("\n⚠️ Some files still need attention before proceeding.")

# Run the async main function
if __name__ == "__main__":
    asyncio.run(main())
