import json
import os
import requests
import time

# Verbatik API configuration
API_KEY = os.getenv("VERBATIK_API_KEY", "vbt_pDMUIEac9JnycAj2Ab5kHDC4XMTsuVkd")
BASE_URL = "https://api.verbatik.com/api/v1/tts"

def generate_audio_verbatik(text: str, output_path: str) -> bool:
    """Generate audio using Verbatik API with Xiaoxiao voice"""
    
    headers = {
        'Content-Type': 'application/ssml+xml',
        'Authorization': f'Bearer {API_KEY}',
        'X-Voice-ID': 'Xiaoxiao'  # Best female Chinese voice for educational content
    }
    
    try:
        response = requests.post(BASE_URL, headers=headers, data=text, timeout=30)
        
        if response.status_code == 200:
            # Save the audio file
            with open(output_path, 'wb') as f:
                f.write(response.content)
            return True
        else:
            print(f"API Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error generating audio: {e}")
        return False

def process_hsk_level(level: int):
    """Process all characters for a specific HSK level"""
    print(f"Processing HSK level {level}...")
    
    # Create directory for this level
    os.makedirs(f'HSK{level}', exist_ok=True)
    
    # Load JSON data
    json_path = f'hsk-json-all/hsk-level-{level}.json'
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
    except FileNotFoundError:
        print(f"❌ JSON file not found: {json_path}")
        return
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON file: {json_path}")
        return
    
    successful = 0
    failed = 0
    total_size = 0
    
    # Generate audio for each character
    for i, char_data in enumerate(characters):
        char_id = char_data.get('id', f"unknown_{i}")
        hanzi = char_data.get('hanzi', '')
        pinyin = char_data.get('pinyin', '')
        
        if not hanzi:
            print(f"⚠️ Skipping entry {char_id}: No hanzi found")
            continue
        
        # Output path
        output_path = f'HSK{level}/{char_id}.mp3'
        
        # Skip if file already exists
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"⏭️ Skipping {hanzi} (ID: {char_id}), file already exists ({file_size} bytes)")
            total_size += file_size
            successful += 1
            continue
        
        print(f"🎵 Generating audio for {hanzi} (ID: {char_id}, Pinyin: {pinyin})")
        
        # Generate audio
        success = generate_audio_verbatik(hanzi, output_path)
        
        if success:
            # Check file size
            file_size = os.path.getsize(output_path)
            total_size += file_size
            size_kb = file_size / 1024
            
            if size_kb <= 4:
                print(f"✅ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Perfect size!")
            elif size_kb <= 8:
                print(f"✅ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Good size")
            else:
                print(f"⚠️ Created audio for {hanzi} ({file_size} bytes / {size_kb:.1f}KB) - Large file")
            
            successful += 1
        else:
            print(f"❌ Failed to generate audio for {hanzi}")
            failed += 1
        
        # Rate limiting: small delay between requests to be respectful
        time.sleep(0.2)
    
    avg_size = (total_size / successful) if successful > 0 else 0
    avg_size_kb = avg_size / 1024
    
    print(f"HSK Level {level} completed:")
    print(f"  ✅ Successful: {successful}")
    print(f"  ❌ Failed: {failed}")
    print(f"  📊 Average file size: {avg_size:.0f} bytes ({avg_size_kb:.1f}KB)")
    print(f"  💾 Total size: {total_size/1024:.1f}KB")

def test_api_connection() -> bool:
    """Test the Verbatik API connection with a simple request"""
    print("🔍 Testing Verbatik API connection...")
    
    test_output = "test_audio.mp3"
    success = generate_audio_verbatik("测试", test_output)
    
    if success and os.path.exists(test_output):
        file_size = os.path.getsize(test_output)
        print(f"✅ API test successful! Generated test file: {file_size} bytes")
        os.remove(test_output)  # Clean up test file
        return True
    else:
        print("❌ API test failed!")
        return False

def main():
    """Main function to process all HSK levels"""
    print("🚀 Starting HSK audio generation with Verbatik (Xiaoxiao voice)...")
    print("🎯 Optimized for Chinese tone clarity and educational purposes")
    
    # Validate API key
    if not API_KEY or len(API_KEY) < 20:
        print("❌ Invalid or missing API key!")
        print("💡 Set your Verbatik API key as environment variable: export VERBATIK_API_KEY=your_key_here")
        return
    
    # Test API connection first
    if not test_api_connection():
        print("❌ Cannot proceed without working API connection")
        return
    
    print(f"🎤 Using Xiaoxiao voice for clear Chinese pronunciation")
    
    # Process each HSK level (1-6)
    start_time = time.time()
    
    for level in range(1, 7):
        level_start = time.time()
        process_hsk_level(level)
        level_time = time.time() - level_start
        print(f"⏱️ HSK{level} completed in {level_time:.1f} seconds\n")
    
    total_time = time.time() - start_time
    print("🎉 All HSK levels processed!")
    print(f"⏱️ Total time: {total_time:.1f} seconds")
    print("💡 Files optimized for HSK learning with clear Chinese tones!")

# Run the main function
if __name__ == "__main__":
    main()
