#!/usr/bin/env python3
"""
Find Missing HSK6 Files
Identifies all missing Opus files in HSK6 to complete the failed files list.
"""

import json
import os
from pathlib import Path

def find_missing_hsk6_files():
    """Find all missing HSK6 files by comparing JSON data with existing Opus files"""
    
    print("🔍 Analyzing HSK6 missing files...")
    
    # Load HSK6 JSON data
    json_path = 'hsk-json-all/hsk-level-6.json'
    if not os.path.exists(json_path):
        print(f"❌ HSK6 JSON file not found: {json_path}")
        return
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            characters = json.load(f)
    except Exception as e:
        print(f"❌ Error loading HSK6 data: {e}")
        return
    
    # Get all expected character IDs
    expected_ids = set()
    for char_data in characters:
        char_id = char_data.get('id')
        if char_id:
            expected_ids.add(char_id)
    
    print(f"📊 Total HSK6 characters in JSON: {len(expected_ids)}")
    
    # Get existing Opus files
    opus_folder = "hsk6_opus"
    existing_opus = set()
    
    if os.path.exists(opus_folder):
        for file in os.listdir(opus_folder):
            if file.endswith('.opus'):
                try:
                    file_id = int(file.replace('.opus', ''))
                    existing_opus.add(file_id)
                except ValueError:
                    continue
    
    print(f"📁 Existing HSK6 Opus files: {len(existing_opus)}")
    
    # Find missing files
    missing_ids = expected_ids - existing_opus
    missing_sorted = sorted(list(missing_ids))
    
    print(f"❌ Missing HSK6 files: {len(missing_sorted)}")
    
    if missing_sorted:
        print(f"\n📋 Complete HSK6 missing files list:")
        print(f"HSK6 missing: {missing_sorted}")
        
        # Format for easy copy-paste into the main script
        print(f"\n📝 For regenerate_failed_hsk_audio.py:")
        print(f"6: {missing_sorted}")
        
        # Show some examples with character data
        print(f"\n🔤 Sample missing characters:")
        for i, char_id in enumerate(missing_sorted[:10]):  # Show first 10
            for char_data in characters:
                if char_data.get('id') == char_id:
                    hanzi = char_data.get('hanzi', '')
                    pinyin = char_data.get('pinyin', '')
                    print(f"  {char_id}: {hanzi} ({pinyin})")
                    break
        
        if len(missing_sorted) > 10:
            print(f"  ... and {len(missing_sorted) - 10} more")
    else:
        print("✅ No missing HSK6 files found!")

if __name__ == "__main__":
    find_missing_hsk6_files()
