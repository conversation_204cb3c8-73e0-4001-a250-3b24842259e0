import json
import os
import asyncio
import edge_tts

# Define the voice to use - <PERSON><PERSON><PERSON><PERSON> is a high-quality female Mandarin voice
VOICE = "zh-CN-XiaoXiaoNeural"

async def generate_audio(text, output_path):
    """Generate audio file using edge-tts with the specified voice"""
    communicate = edge_tts.Communicate(text, VOICE)
    await communicate.save(output_path)
    # Small delay to avoid rate limiting
    await asyncio.sleep(0.2)

async def process_hsk_level(level):
    """Process all characters for a specific HSK level"""
    print(f"Processing HSK level {level}...")
    
    # Create directory for this level
    os.makedirs(f'assets/audio/hsk{level}', exist_ok=True)
    
    # Load JSON data
    json_path = f'assets/hsk-json-all/hsk-level-{level}.json'
    with open(json_path, 'r', encoding='utf-8') as f:
        characters = json.load(f)
    
    # Generate audio for each character
    for char_data in characters:
        char_id = char_data['id']
        hanzi = char_data['hanzi']
        pinyin = char_data['pinyin']
        
        # Output path
        output_path = f'assets/audio/hsk{level}/{char_id}.mp3'
        
        # Skip if file already exists
        if os.path.exists(output_path):
            print(f"Skipping {hanzi} (ID: {char_id}), file already exists")
            continue
        
        print(f"Generating audio for {hanzi} (ID: {char_id}, Pinyin: {pinyin})")
        
        try:
            # Generate audio
            await generate_audio(hanzi, output_path)
            print(f"✅ Created audio for {hanzi}")
            
        except Exception as e:
            print(f"❌ Error generating audio for {hanzi}: {e}")

async def main():
    """Main function to process all HSK levels"""
    # Process each HSK level (1-6)
    for level in range(1, 7):
        await process_hsk_level(level)
    
    print("All audio files generated successfully!")

# Run the async main function
if __name__ == "__main__":
    asyncio.run(main()) 